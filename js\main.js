// ===== MAIN JAVASCRIPT FILE =====

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initAnimations();
    initScrollAnimations();
    initContactForm();
    initCounterAnimation();
    initSmoothScrolling();
});

// ===== NAVIGATION FUNCTIONALITY =====
function initNavigation() {
    const mobileMenu = document.getElementById('mobile-menu');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    const navbar = document.getElementById('navbar');

    // Mobile menu toggle
    mobileMenu.addEventListener('click', function() {
        mobileMenu.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });

    // Active nav link highlighting
    window.addEventListener('scroll', function() {
        let current = '';
        const sections = document.querySelectorAll('section');
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
}

// ===== INITIAL PAGE LOAD ANIMATIONS =====
function initAnimations() {
    // Hero section animations
    anime.timeline({
        easing: 'easeOutExpo',
        duration: 1000
    })
    .add({
        targets: '.hero-line',
        translateY: [100, 0],
        opacity: [0, 1],
        delay: anime.stagger(200, {start: 500})
    })
    .add({
        targets: '.hero-subtitle',
        translateY: [50, 0],
        opacity: [0, 1],
        duration: 800
    }, '-=600')
    .add({
        targets: '.hero-buttons .btn',
        translateY: [30, 0],
        opacity: [0, 1],
        delay: anime.stagger(100),
        duration: 600
    }, '-=400')
    .add({
        targets: '.floating-element',
        scale: [0, 1],
        opacity: [0, 0.8],
        delay: anime.stagger(200),
        duration: 800
    }, '-=800');

    // Navbar animation
    anime({
        targets: '.navbar',
        translateY: [-100, 0],
        opacity: [0, 1],
        duration: 800,
        easing: 'easeOutExpo',
        delay: 200
    });

    // Scroll indicator animation
    anime({
        targets: '.scroll-indicator',
        opacity: [0, 1],
        translateY: [20, 0],
        duration: 1000,
        delay: 2000,
        easing: 'easeOutExpo'
    });
}

// ===== SCROLL-TRIGGERED ANIMATIONS =====
function initScrollAnimations() {
    // Create intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                
                // Services section animation
                if (target.classList.contains('services')) {
                    animateServices();
                }
                
                // Portfolio section animation
                if (target.classList.contains('portfolio')) {
                    animatePortfolio();
                }
                
                // About section animation
                if (target.classList.contains('about')) {
                    animateAbout();
                }
                
                // Contact section animation
                if (target.classList.contains('contact')) {
                    animateContact();
                }
                
                observer.unobserve(target);
            }
        });
    }, observerOptions);

    // Observe all sections
    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });
}

// ===== SECTION-SPECIFIC ANIMATIONS =====
function animateServices() {
    anime.timeline({
        easing: 'easeOutExpo'
    })
    .add({
        targets: '.services .section-title',
        translateY: [50, 0],
        opacity: [0, 1],
        duration: 800
    })
    .add({
        targets: '.services .section-subtitle',
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600
    }, '-=400')
    .add({
        targets: '.service-card',
        translateY: [60, 0],
        opacity: [0, 1],
        delay: anime.stagger(150),
        duration: 800
    }, '-=200');

    // Service card hover animations
    document.querySelectorAll('.service-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            anime({
                targets: this.querySelector('.service-icon'),
                scale: [1, 1.1],
                rotate: [0, 5],
                duration: 300,
                easing: 'easeOutQuad'
            });
        });

        card.addEventListener('mouseleave', function() {
            anime({
                targets: this.querySelector('.service-icon'),
                scale: [1.1, 1],
                rotate: [5, 0],
                duration: 300,
                easing: 'easeOutQuad'
            });
        });
    });
}

function animatePortfolio() {
    anime.timeline({
        easing: 'easeOutExpo'
    })
    .add({
        targets: '.portfolio .section-title',
        translateY: [50, 0],
        opacity: [0, 1],
        duration: 800
    })
    .add({
        targets: '.portfolio .section-subtitle',
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600
    }, '-=400')
    .add({
        targets: '.portfolio-item',
        scale: [0.8, 1],
        opacity: [0, 1],
        delay: anime.stagger(100),
        duration: 800
    }, '-=200');

    // Portfolio item hover animations
    document.querySelectorAll('.portfolio-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            anime({
                targets: this.querySelector('.portfolio-overlay'),
                opacity: [0, 1],
                duration: 300,
                easing: 'easeOutQuad'
            });
            
            anime({
                targets: this.querySelector('.portfolio-title'),
                translateY: [20, 0],
                duration: 400,
                delay: 100,
                easing: 'easeOutQuad'
            });
            
            anime({
                targets: this.querySelector('.portfolio-link'),
                scale: [0.8, 1],
                duration: 400,
                delay: 200,
                easing: 'easeOutQuad'
            });
        });
    });
}

function animateAbout() {
    anime.timeline({
        easing: 'easeOutExpo'
    })
    .add({
        targets: '.about .section-title',
        translateX: [-50, 0],
        opacity: [0, 1],
        duration: 800
    })
    .add({
        targets: '.about-description',
        translateX: [-30, 0],
        opacity: [0, 1],
        duration: 600
    }, '-=400')
    .add({
        targets: '.about-shapes .shape',
        scale: [0, 1],
        opacity: [0, 1],
        delay: anime.stagger(200),
        duration: 800
    }, '-=600');
}

function animateContact() {
    anime.timeline({
        easing: 'easeOutExpo'
    })
    .add({
        targets: '.contact .section-title',
        translateY: [50, 0],
        opacity: [0, 1],
        duration: 800
    })
    .add({
        targets: '.contact .section-subtitle',
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600
    }, '-=400')
    .add({
        targets: '.contact-info .contact-item',
        translateX: [-40, 0],
        opacity: [0, 1],
        delay: anime.stagger(100),
        duration: 600
    }, '-=200')
    .add({
        targets: '.contact-form',
        translateX: [40, 0],
        opacity: [0, 1],
        duration: 800
    }, '-=600');
}

// ===== COUNTER ANIMATION =====
function initCounterAnimation() {
    const stats = document.querySelectorAll('.stat-number');
    let hasAnimated = false;

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !hasAnimated) {
                hasAnimated = true;
                stats.forEach(stat => {
                    const target = parseInt(stat.getAttribute('data-target'));

                    anime({
                        targets: stat,
                        innerHTML: [0, target],
                        duration: 2000,
                        round: 1,
                        easing: 'easeOutExpo',
                        update: function(anim) {
                            stat.innerHTML = Math.round(anim.animatables[0].target.innerHTML);
                        }
                    });
                });
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    const aboutSection = document.querySelector('.about');
    if (aboutSection) {
        observer.observe(aboutSection);
    }
}

// ===== CONTACT FORM FUNCTIONALITY =====
function initContactForm() {
    const form = document.getElementById('contact-form');
    const inputs = form.querySelectorAll('input, textarea');

    // Form input animations
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            anime({
                targets: this,
                scale: [1, 1.02],
                duration: 200,
                easing: 'easeOutQuad'
            });
        });

        input.addEventListener('blur', function() {
            anime({
                targets: this,
                scale: [1.02, 1],
                duration: 200,
                easing: 'easeOutQuad'
            });
        });
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        // Button loading animation
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        anime({
            targets: submitBtn,
            scale: [1, 0.95, 1],
            duration: 600,
            easing: 'easeInOutQuad'
        });

        // Simulate form submission (replace with actual form handling)
        setTimeout(() => {
            submitBtn.textContent = 'Message Sent!';
            submitBtn.style.backgroundColor = '#10b981';

            anime({
                targets: form,
                scale: [1, 1.02, 1],
                duration: 400,
                easing: 'easeInOutQuad'
            });

            // Reset form after delay
            setTimeout(() => {
                form.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                submitBtn.style.backgroundColor = '';
            }, 2000);
        }, 1500);
    });
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));

            if (target) {
                const offsetTop = target.offsetTop - 80; // Account for fixed navbar

                anime({
                    targets: document.documentElement,
                    scrollTop: offsetTop,
                    duration: 800,
                    easing: 'easeInOutQuad'
                });
            }
        });
    });

    // Scroll indicator click
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const servicesSection = document.querySelector('#services');
            if (servicesSection) {
                anime({
                    targets: document.documentElement,
                    scrollTop: servicesSection.offsetTop - 80,
                    duration: 1000,
                    easing: 'easeInOutQuad'
                });
            }
        });
    }
}

// ===== BUTTON HOVER ANIMATIONS =====
document.addEventListener('DOMContentLoaded', function() {
    // Button hover effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            anime({
                targets: this,
                scale: [1, 1.05],
                duration: 200,
                easing: 'easeOutQuad'
            });
        });

        btn.addEventListener('mouseleave', function() {
            anime({
                targets: this,
                scale: [1.05, 1],
                duration: 200,
                easing: 'easeOutQuad'
            });
        });
    });

    // Logo hover animation
    document.querySelectorAll('.logo').forEach(logo => {
        logo.addEventListener('mouseenter', function() {
            anime({
                targets: this,
                rotate: [0, 5],
                scale: [1, 1.05],
                duration: 300,
                easing: 'easeOutQuad'
            });
        });

        logo.addEventListener('mouseleave', function() {
            anime({
                targets: this,
                rotate: [5, 0],
                scale: [1.05, 1],
                duration: 300,
                easing: 'easeOutQuad'
            });
        });
    });
});

// ===== FLOATING ELEMENTS ANIMATION =====
function animateFloatingElements() {
    const elements = document.querySelectorAll('.floating-element');

    elements.forEach((element, index) => {
        anime({
            targets: element,
            translateY: [0, -20, 0],
            rotate: [0, 180, 360],
            duration: 4000 + (index * 1000),
            loop: true,
            easing: 'easeInOutSine',
            delay: index * 500
        });
    });
}

// Start floating animation after page load
window.addEventListener('load', function() {
    setTimeout(animateFloatingElements, 2000);
});

// ===== PERFORMANCE OPTIMIZATIONS =====
// Throttle scroll events for better performance
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Apply throttling to scroll events
window.addEventListener('scroll', throttle(function() {
    // Scroll-dependent animations can be added here
}, 16)); // ~60fps
