# Creative Agency Website

A modern, responsive website for a creative agency built with HTML, CSS, and anime.js animations. Designed for static hosting with optimal performance and stunning visual effects.

## 🚀 Features

- **Modern Design**: Clean, professional layout with contemporary aesthetics
- **Smooth Animations**: Powered by anime.js for fluid, engaging interactions
- **Fully Responsive**: Mobile-first design that works on all devices
- **Performance Optimized**: Fast loading times and smooth scrolling
- **Static Hosting Ready**: No backend dependencies, perfect for static hosts
- **Custom Typography**: Uses Bricolage Grotesque font family
- **Interactive Elements**: Hover effects, scroll animations, and form interactions

## 📁 Project Structure

```
/
├── index.html              # Main HTML file
├── css/
│   └── styles.css         # All CSS styles and responsive design
├── js/
│   └── main.js           # JavaScript functionality and animations
├── fonts/                # Bricolage Grotesque font files
│   ├── BricolageGrotesque-Regular.woff2
│   ├── BricolageGrotesque-Medium.woff2
│   ├── BricolageGrotesque-SemiBold.woff2
│   ├── BricolageGrotesque-Bold.woff2
│   └── BricolageGrotesque-ExtraBold.woff2
├── images/               # Image assets
│   ├── favicon.png
│   └── logo-black.png
└── README.md            # Project documentation
```

## 🛠 Tech Stack

- **HTML5**: Semantic markup and accessibility
- **CSS3**: Modern CSS with custom properties, Grid, and Flexbox
- **JavaScript (ES6+)**: Modern JavaScript features
- **Anime.js**: Lightweight animation library
- **Bricolage Grotesque**: Custom web fonts

## 🎨 Sections

1. **Hero Section**: Eye-catching introduction with animated elements
2. **Services**: Showcase of agency capabilities with hover effects
3. **Portfolio**: Featured work with interactive overlays
4. **About**: Company information with animated statistics
5. **Contact**: Contact form and information
6. **Footer**: Social links and copyright

## 🚀 Getting Started

### Prerequisites

- A modern web browser
- A static web server (optional for local development)

### Installation

1. Clone or download the project files
2. Ensure all files maintain the directory structure shown above
3. Open `index.html` in a web browser or serve via a static web server

### For Local Development

You can use any static server. Here are a few options:

**Using Python:**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**Using Node.js (with live-server):**
```bash
npm install -g live-server
live-server
```

**Using PHP:**
```bash
php -S localhost:8000
```

Then visit `http://localhost:8000` in your browser.

## 🌐 Deployment

This website is ready for deployment to any static hosting service:

- **Netlify**: Drag and drop the entire folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **AWS S3**: Upload files to an S3 bucket with static hosting enabled
- **Firebase Hosting**: Use Firebase CLI to deploy

## 🎯 Customization

### Colors
Edit the CSS custom properties in `css/styles.css`:
```css
:root {
  --primary-color: #6366f1;
  --secondary-color: #f59e0b;
  --accent-color: #ec4899;
  /* ... other colors */
}
```

### Content
- Update text content in `index.html`
- Replace logo and favicon in the `images/` folder
- Modify contact information in the contact section

### Animations
- Adjust animation timing in `js/main.js`
- Modify anime.js parameters for different effects
- Add new animations using the anime.js API

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Performance Features

- **Optimized Fonts**: Preloaded critical fonts with font-display: swap
- **Efficient Animations**: Hardware-accelerated CSS and anime.js animations
- **Responsive Images**: Properly sized images for different screen sizes
- **Throttled Scroll Events**: Performance-optimized scroll listeners
- **Intersection Observer**: Efficient scroll-triggered animations

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

If you have any questions or need help with customization, please open an issue in the repository.

---

**Built with ❤️ for creative agencies worldwide**
