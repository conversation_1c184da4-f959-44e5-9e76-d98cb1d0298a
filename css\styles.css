/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --secondary-color: #f59e0b;
  --accent-color: #ec4899;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --background-primary: #ffffff;
  --background-secondary: #f9fafb;
  --background-dark: #111827;
  --border-color: #e5e7eb;
  
  /* Typography */
  --font-primary: 'Bricolage Grotesque', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Layout */
  --container-max-width: 1200px;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== FONT FACE DECLARATIONS ===== */
@font-face {
  font-family: 'Bricolage Grotesque';
  src: url('../fonts/BricolageGrotesque-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bricolage Grotesque';
  src: url('../fonts/BricolageGrotesque-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bricolage Grotesque';
  src: url('../fonts/BricolageGrotesque-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bricolage Grotesque';
  src: url('../fonts/BricolageGrotesque-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bricolage Grotesque';
  src: url('../fonts/BricolageGrotesque-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-primary);
  overflow-x: hidden;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-normal);
}

ul {
  list-style: none;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
  cursor: pointer;
  text-decoration: none;
  border: 2px solid transparent;
  min-height: 48px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: var(--transition-normal);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.nav-logo .logo {
  height: 40px;
  width: auto;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-link {
  font-weight: 500;
  color: var(--text-primary);
  position: relative;
  padding: var(--spacing-xs) 0;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  transition: var(--transition-normal);
  border-radius: 2px;
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
  overflow: hidden;
}

.hero-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  min-height: 80vh;
}

.hero-content {
  z-index: 2;
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
}

.hero-line {
  display: block;
  overflow: hidden;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-visual {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-element {
  position: absolute;
  border-radius: var(--border-radius-lg);
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  opacity: 0.8;
}

.element-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 20%;
  animation: float 6s ease-in-out infinite;
}

.element-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 30%;
  animation: float 8s ease-in-out infinite reverse;
}

.element-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 10%;
  animation: float 7s ease-in-out infinite;
}

.element-4 {
  width: 60px;
  height: 60px;
  top: 30%;
  right: 10%;
  animation: float 5s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  cursor: pointer;
}

.scroll-arrow {
  width: 20px;
  height: 20px;
  border-right: 2px solid var(--text-secondary);
  border-bottom: 2px solid var(--text-secondary);
  transform: rotate(45deg);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: rotate(45deg) translateY(0);
  }
  40% {
    transform: rotate(45deg) translateY(-10px);
  }
  60% {
    transform: rotate(45deg) translateY(-5px);
  }
}

/* ===== SERVICES SECTION ===== */
.services {
  padding: var(--spacing-4xl) 0;
  background-color: var(--background-secondary);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-2xl);
}

.service-card {
  background-color: var(--background-primary);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  text-align: center;
  border: 1px solid var(--border-color);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.icon-shape {
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: var(--border-radius);
}

.service-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.service-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
  padding: var(--spacing-4xl) 0;
  background-color: var(--background-primary);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.portfolio-item {
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  aspect-ratio: 4/3;
  cursor: pointer;
  transition: var(--transition-normal);
}

.portfolio-item:hover {
  transform: scale(1.02);
}

.portfolio-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
  color: white;
  text-align: center;
  padding: var(--spacing-xl);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.portfolio-category {
  font-size: var(--font-size-sm);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-lg);
}

.portfolio-link {
  color: white;
  border: 2px solid white;
  padding: var(--spacing-xs) var(--spacing-lg);
  border-radius: var(--border-radius);
  transition: var(--transition-normal);
}

.portfolio-link:hover {
  background-color: white;
  color: var(--text-primary);
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--spacing-4xl) 0;
  background-color: var(--background-secondary);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
}

.about-text .section-title {
  text-align: left;
  margin-bottom: var(--spacing-lg);
}

.about-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.7;
}

.stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.about-visual {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: var(--border-radius-lg);
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

.shape-1 {
  width: 150px;
  height: 150px;
  top: 20%;
  left: 10%;
  animation: float 8s ease-in-out infinite;
}

.shape-2 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 20%;
  animation: float 6s ease-in-out infinite reverse;
}

.shape-3 {
  width: 120px;
  height: 120px;
  bottom: 30%;
  right: 10%;
  animation: float 7s ease-in-out infinite;
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--spacing-4xl) 0;
  background-color: var(--background-primary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-4xl);
  margin-top: var(--spacing-2xl);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.contact-item h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.contact-item p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.contact-form {
  background-color: var(--background-secondary);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  transition: var(--transition-normal);
  background-color: var(--background-primary);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--background-dark);
  color: white;
  padding: var(--spacing-2xl) 0;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.footer-logo .logo {
  height: 32px;
  filter: brightness(0) invert(1);
}

.footer-text {
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.social-links {
  display: flex;
  gap: var(--spacing-lg);
}

.social-link {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
}

.social-link:hover {
  color: var(--primary-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero-visual {
    height: 300px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background-color: var(--background-primary);
    width: 100%;
    text-align: center;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl) 0;
    border-top: 1px solid var(--border-color);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .nav-container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .service-card,
  .contact-form {
    padding: var(--spacing-lg);
  }

  .btn {
    width: 100%;
    max-width: 300px;
  }
}

/* ===== ANIMATIONS & UTILITIES ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

/* Smooth scrolling for older browsers */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}
